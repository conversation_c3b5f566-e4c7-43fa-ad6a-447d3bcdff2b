basePath: /
definitions:
  clients.AIChoice:
    properties:
      finish_reason:
        type: string
      index:
        type: integer
      message:
        $ref: '#/definitions/clients.AIMessage'
      text:
        type: string
      tool_calls:
        description: Function calls in streaming mode
        items:
          $ref: '#/definitions/clients.ToolCall'
        type: array
    type: object
  clients.AIError:
    properties:
      code:
        type: string
      message:
        type: string
      type:
        type: string
    type: object
  clients.AIMessage:
    properties:
      content:
        example: Hello, how are you?
        type: string
      name:
        description: Name of the function for tool messages
        type: string
      role:
        enum:
        - system
        - user
        - assistant
        - tool
        example: user
        type: string
      tool_call_id:
        description: ID of the tool call this message is responding to
        type: string
      tool_calls:
        description: Function calls made by assistant
        items:
          $ref: '#/definitions/clients.ToolCall'
        type: array
    required:
    - role
    type: object
  clients.AIModel:
    properties:
      created:
        type: integer
      id:
        type: string
      object:
        type: string
      owned_by:
        type: string
      permission:
        items: {}
        type: array
    type: object
  clients.AIResponse:
    properties:
      choices:
        items:
          $ref: '#/definitions/clients.AIChoice'
        type: array
      created:
        type: integer
      error:
        $ref: '#/definitions/clients.AIError'
      id:
        type: string
      model:
        type: string
      object:
        type: string
      usage:
        $ref: '#/definitions/clients.AIUsage'
    type: object
  clients.AIUsage:
    properties:
      completion_tokens:
        type: integer
      prompt_tokens:
        type: integer
      total_tokens:
        type: integer
    type: object
  clients.AnthropicCacheControl:
    properties:
      ttl:
        description: '"5m", "1h"'
        type: string
      type:
        description: '"ephemeral"'
        type: string
    type: object
  clients.AnthropicCacheCreation:
    properties:
      ephemeral_1h_input_tokens:
        type: integer
      ephemeral_5m_input_tokens:
        type: integer
    type: object
  clients.AnthropicContainer:
    properties:
      expires_at:
        type: string
      id:
        type: string
    type: object
  clients.AnthropicContentBlock:
    properties:
      content: {}
      id:
        example: toolu_01A09q90qw90lq917835lq9
        type: string
      input: {}
      is_error:
        type: boolean
      name:
        example: get_weather
        type: string
      source:
        properties:
          data:
            type: string
          media_type:
            example: image/jpeg
            type: string
          type:
            example: base64
            type: string
        type: object
      text:
        example: Hello, how can I help you?
        type: string
      tool_use_id:
        type: string
      type:
        example: text
        type: string
    type: object
  clients.AnthropicMCPServer:
    properties:
      authorization_token:
        type: string
      name:
        type: string
      tool_configuration:
        $ref: '#/definitions/clients.AnthropicToolConfiguration'
      type:
        description: '"url"'
        type: string
      url:
        type: string
    required:
    - name
    - type
    - url
    type: object
  clients.AnthropicMessage:
    properties:
      content:
        example: Hello, how are you?
        type: string
      role:
        example: user
        type: string
    type: object
  clients.AnthropicMessageRequest:
    properties:
      container:
        type: string
      max_tokens:
        example: 1024
        maximum: 4096
        minimum: 1
        type: integer
      mcp_servers:
        items:
          $ref: '#/definitions/clients.AnthropicMCPServer'
        type: array
      messages:
        items:
          $ref: '#/definitions/clients.AnthropicMessage'
        minItems: 1
        type: array
      metadata:
        $ref: '#/definitions/clients.AnthropicMetadata'
      model:
        example: claude-3-sonnet-20240229
        type: string
      service_tier:
        example: auto
        type: string
      stop_sequences:
        example:
        - '["\n\n"]'
        items:
          type: string
        type: array
      stream:
        example: false
        type: boolean
      system:
        example: You are a helpful assistant.
        type: string
      temperature:
        example: 0.7
        maximum: 1
        minimum: 0
        type: number
      thinking:
        $ref: '#/definitions/clients.AnthropicThinkingConfig'
      tool_choice:
        example: auto
        type: string
      tools:
        items:
          $ref: '#/definitions/clients.AnthropicTool'
        type: array
      top_k:
        example: 5
        minimum: 1
        type: integer
      top_p:
        example: 0.7
        maximum: 1
        minimum: 0
        type: number
    required:
    - max_tokens
    - messages
    - model
    type: object
  clients.AnthropicMessageResponse:
    properties:
      container:
        $ref: '#/definitions/clients.AnthropicContainer'
      content:
        items:
          $ref: '#/definitions/clients.AnthropicContentBlock'
        type: array
      id:
        example: msg_013Zva2CMHLNnXjNJJKqJ2EF
        type: string
      model:
        example: claude-3-sonnet-20240229
        type: string
      role:
        example: assistant
        type: string
      stop_reason:
        example: end_turn
        type: string
      stop_sequence:
        type: string
      type:
        example: message
        type: string
      usage:
        $ref: '#/definitions/clients.AnthropicUsage'
    type: object
  clients.AnthropicMetadata:
    properties:
      user_id:
        description: 外部用户标识符
        type: string
    type: object
  clients.AnthropicServerToolUse:
    properties:
      web_search_requests:
        type: integer
    type: object
  clients.AnthropicThinkingConfig:
    properties:
      budget_tokens:
        minimum: 1024
        type: integer
      type:
        description: '"enabled"'
        type: string
    required:
    - budget_tokens
    - type
    type: object
  clients.AnthropicTool:
    properties:
      cache_control:
        $ref: '#/definitions/clients.AnthropicCacheControl'
      description:
        type: string
      input_schema:
        additionalProperties: true
        type: object
      name:
        type: string
      type:
        description: '"custom" 等'
        type: string
    required:
    - input_schema
    - name
    type: object
  clients.AnthropicToolConfiguration:
    properties:
      allowed_tools:
        items:
          type: string
        type: array
      enabled:
        type: boolean
    type: object
  clients.AnthropicUsage:
    properties:
      cache_creation:
        $ref: '#/definitions/clients.AnthropicCacheCreation'
      cache_creation_input_tokens:
        type: integer
      cache_read_input_tokens:
        type: integer
      input_tokens:
        example: 10
        type: integer
      output_tokens:
        example: 25
        type: integer
      server_tool_use:
        $ref: '#/definitions/clients.AnthropicServerToolUse'
      service_tier:
        example: standard
        type: string
    type: object
  clients.ChatCompletionRequest:
    properties:
      max_tokens:
        example: 150
        type: integer
      messages:
        items:
          $ref: '#/definitions/clients.AIMessage'
        minItems: 1
        type: array
      model:
        example: gpt-3.5-turbo
        type: string
      stream:
        example: false
        type: boolean
      temperature:
        example: 0.7
        type: number
      tool_choice:
        description: Tool choice strategy
      tools:
        description: Function call tools
        items:
          $ref: '#/definitions/clients.Tool'
        type: array
      web_search:
        description: 是否启用联网搜索
        example: false
        type: boolean
    required:
    - messages
    - model
    type: object
  clients.CompletionRequest:
    properties:
      max_tokens:
        example: 150
        type: integer
      model:
        example: gpt-3.5-turbo
        type: string
      prompt:
        example: Once upon a time
        type: string
      stream:
        example: false
        type: boolean
      temperature:
        example: 0.7
        type: number
      web_search:
        description: 是否启用联网搜索
        example: false
        type: boolean
    required:
    - model
    - prompt
    type: object
  clients.Function:
    properties:
      description:
        example: Search for information
        type: string
      name:
        example: search
        type: string
      parameters:
        description: JSON Schema for function parameters
    type: object
  clients.FunctionCall:
    properties:
      arguments:
        description: JSON string of function arguments
        type: string
      name:
        example: search
        type: string
    type: object
  clients.ModelsResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/clients.AIModel'
        type: array
      object:
        type: string
    type: object
  clients.Tool:
    properties:
      function:
        $ref: '#/definitions/clients.Function'
      type:
        example: function
        type: string
    type: object
  clients.ToolCall:
    properties:
      function:
        $ref: '#/definitions/clients.FunctionCall'
      id:
        example: call_123
        type: string
      type:
        example: function
        type: string
    type: object
  dto.ChangePasswordRequest:
    properties:
      new_password:
        maxLength: 100
        minLength: 6
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  dto.ErrorInfo:
    properties:
      code:
        type: string
      details:
        additionalProperties: true
        type: object
      message:
        type: string
    type: object
  dto.FileDeleteRequest:
    properties:
      key:
        description: S3对象键
        example: uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
    required:
    - key
    type: object
  dto.FileDeleteResponse:
    properties:
      key:
        description: 已删除的S3对象键
        example: uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
      message:
        description: 删除结果消息
        example: File deleted successfully
        type: string
    type: object
  dto.FileInfoResponse:
    properties:
      filename:
        description: 原始文件名
        example: avatar.jpg
        type: string
      key:
        description: S3对象键
        example: uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
      mime_type:
        description: MIME类型
        example: image/jpeg
        type: string
      size:
        description: 文件大小（字节）
        example: 1024000
        type: integer
      url:
        description: 文件访问URL
        example: https://your-bucket.s3.us-east-1.amazonaws.com/uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
    type: object
  dto.FileUploadResponse:
    properties:
      filename:
        description: 原始文件名
        example: avatar.jpg
        type: string
      key:
        description: S3对象键
        example: uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
      mime_type:
        description: MIME类型
        example: image/jpeg
        type: string
      size:
        description: 文件大小（字节）
        example: 1024000
        type: integer
      uploaded_at:
        description: 上传时间
        type: string
      url:
        description: 文件访问URL
        example: https://your-bucket.s3.us-east-1.amazonaws.com/uploads/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg
        type: string
    type: object
  dto.GetUserProfileResponse:
    properties:
      balance:
        type: number
      created_at:
        type: string
      email:
        type: string
      full_name:
        type: string
      id:
        type: integer
      updated_at:
        type: string
      username:
        type: string
    type: object
  dto.LoginRequest:
    properties:
      password:
        maxLength: 100
        minLength: 6
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - password
    - username
    type: object
  dto.LoginResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
      user:
        $ref: '#/definitions/dto.UserInfo'
    type: object
  dto.OAuthLoginRequest:
    properties:
      code:
        type: string
      provider:
        enum:
        - google
        - github
        type: string
      state:
        type: string
    required:
    - code
    - provider
    - state
    type: object
  dto.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  dto.RefreshTokenResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
  dto.RegisterRequest:
    properties:
      email:
        type: string
      password:
        maxLength: 100
        minLength: 6
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    type: object
  dto.RegisterResponse:
    properties:
      created_at:
        type: string
      email:
        type: string
      full_name:
        type: string
      id:
        type: integer
      message:
        type: string
      username:
        type: string
    type: object
  dto.RegisterWithCodeRequest:
    properties:
      email:
        type: string
      password:
        maxLength: 100
        minLength: 6
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
      verification_code:
        type: string
    required:
    - email
    - password
    - verification_code
    type: object
  dto.ResetPasswordRequest:
    properties:
      email:
        type: string
      new_password:
        maxLength: 100
        minLength: 6
        type: string
      verification_code:
        type: string
    required:
    - email
    - new_password
    - verification_code
    type: object
  dto.ResetPasswordResponse:
    properties:
      message:
        type: string
    type: object
  dto.Response:
    properties:
      data: {}
      error:
        $ref: '#/definitions/dto.ErrorInfo'
      message:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  dto.SendVerificationCodeRequest:
    properties:
      email:
        type: string
      type:
        enum:
        - register
        - password_reset
        type: string
    required:
    - email
    - type
    type: object
  dto.SendVerificationCodeResponse:
    properties:
      expires_in:
        description: 过期时间（秒）
        type: integer
      message:
        type: string
    type: object
  dto.UsageResponse:
    properties:
      total_cost:
        example: 1.25
        type: number
      total_requests:
        example: 100
        type: integer
      total_tokens:
        example: 5000
        type: integer
    type: object
  dto.UserInfo:
    properties:
      email:
        type: string
      full_name:
        type: string
      id:
        type: integer
      username:
        type: string
    type: object
  dto.UserRechargeRequest:
    properties:
      amount:
        type: number
      description:
        type: string
    required:
    - amount
    type: object
  dto.VerifyCodeRequest:
    properties:
      code:
        type: string
      email:
        type: string
      type:
        enum:
        - register
        - password_reset
        type: string
    required:
    - code
    - email
    - type
    type: object
  dto.VerifyCodeResponse:
    properties:
      message:
        type: string
      valid:
        type: boolean
    type: object
  handlers.ActionRequest:
    properties:
      customId:
        type: string
      notifyHook:
        type: string
      state:
        type: string
      taskId:
        type: string
    required:
    - customId
    - taskId
    type: object
  handlers.BlendRequest:
    properties:
      base64Array:
        items:
          type: string
        maxItems: 5
        minItems: 2
        type: array
      botType:
        type: string
      dimensions:
        type: string
      notifyHook:
        type: string
      state:
        type: string
    required:
    - base64Array
    type: object
  handlers.CancelRequest:
    properties:
      taskId:
        type: string
    required:
    - taskId
    type: object
  handlers.DescribeRequest:
    properties:
      base64:
        type: string
      botType:
        type: string
      notifyHook:
        type: string
      state:
        type: string
    required:
    - base64
    type: object
  handlers.ImagineRequest:
    properties:
      base64Array:
        items:
          type: string
        type: array
      botType:
        type: string
      notifyHook:
        type: string
      prompt:
        type: string
      state:
        type: string
    required:
    - prompt
    type: object
  handlers.MJButton:
    properties:
      customId:
        type: string
      emoji:
        type: string
      label:
        type: string
      style:
        type: integer
      type:
        type: integer
    type: object
  handlers.MJImageURL:
    properties:
      url:
        type: string
    type: object
  handlers.MJResponse:
    properties:
      code:
        type: integer
      description:
        type: string
      properties:
        additionalProperties: true
        type: object
      result: {}
    type: object
  handlers.MJTaskResponse:
    properties:
      action:
        type: string
      botType:
        type: string
      buttons:
        items:
          $ref: '#/definitions/handlers.MJButton'
        type: array
      customId:
        type: string
      description:
        type: string
      failReason:
        type: string
      finishTime:
        type: integer
      id:
        type: string
      imageHeight:
        type: integer
      imageUrl:
        type: string
      imageUrls:
        items:
          $ref: '#/definitions/handlers.MJImageURL'
        type: array
      imageWidth:
        type: integer
      maskBase64:
        type: string
      mode:
        type: string
      progress:
        type: string
      prompt:
        type: string
      promptEn:
        type: string
      proxy:
        type: string
      startTime:
        type: integer
      state:
        type: string
      status:
        type: string
      submitTime:
        type: integer
      videoUrl:
        type: string
      videoUrls:
        items:
          type: string
        type: array
    type: object
  handlers.ModalRequest:
    properties:
      base64:
        type: string
      botType:
        type: string
      maskBase64:
        type: string
      notifyHook:
        type: string
      prompt:
        type: string
      state:
        type: string
    required:
    - base64
    - maskBase64
    - prompt
    type: object
host: api-dev.718ai.cn
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: https://example.com/support
  description: AI API Gateway provides unified access to multiple AI services including
    OpenAI-compatible chat completions, Midjourney image generation, and more.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://example.com/terms
  title: AI API Gateway
  version: 1.0.0
paths:
  /api/files/{key}:
    get:
      description: 根据文件键获取文件信息和访问URL
      parameters:
      - description: 文件键
        in: path
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.FileInfoResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "404":
          description: 文件不存在
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 获取文件信息
      tags:
      - 文件管理
  /api/files/delete:
    delete:
      consumes:
      - application/json
      description: 根据文件键删除S3中的文件
      parameters:
      - description: 删除请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.FileDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.FileDeleteResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "404":
          description: 文件不存在
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 删除S3文件
      tags:
      - 文件管理
  /api/files/upload:
    post:
      consumes:
      - multipart/form-data
      description: 上传文件到S3存储，支持图片、PDF等多种文件类型
      parameters:
      - description: 要上传的文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.FileUploadResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/dto.Response'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 上传文件到S3
      tags:
      - 文件管理
  /auth/change-password:
    post:
      consumes:
      - application/json
      description: 修改当前用户的密码
      parameters:
      - description: 修改密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/dto.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 未认证或旧密码错误
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 修改用户密码
      tags:
      - 认证
  /auth/login:
    post:
      consumes:
      - application/json
      description: 使用用户名和密码进行登录
      parameters:
      - description: 登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 用户登录
      tags:
      - 认证
  /auth/oauth/{provider}/callback:
    get:
      description: 处理OAuth提供商的回调，从查询参数获取code和state
      parameters:
      - description: OAuth提供商
        enum:
        - google
        - github
        in: path
        name: provider
        required: true
        type: string
      - description: 授权码
        in: query
        name: code
        required: true
        type: string
      - description: 状态参数
        in: query
        name: state
        required: true
        type: string
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 从查询参数处理OAuth回调
      tags:
      - OAuth
    post:
      consumes:
      - application/json
      description: 处理OAuth提供商的回调，完成用户登录
      parameters:
      - description: OAuth提供商
        enum:
        - google
        - github
        in: path
        name: provider
        required: true
        type: string
      - description: OAuth回调请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.OAuthLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 处理OAuth回调
      tags:
      - OAuth
  /auth/oauth/{provider}/redirect:
    get:
      description: 获取指定提供商的OAuth认证URL并重定向
      parameters:
      - description: OAuth提供商
        enum:
        - google
        - github
        in: path
        name: provider
        required: true
        type: string
      responses:
        "302":
          description: 重定向到OAuth提供商
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 从查询参数获取OAuth认证URL
      tags:
      - OAuth
  /auth/oauth/{provider}/url:
    get:
      consumes:
      - application/json
      description: 获取指定提供商的OAuth认证URL
      parameters:
      - description: OAuth提供商
        enum:
        - google
        - github
        in: path
        name: provider
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/dto.Response'
            - properties:
                data:
                  properties:
                    auth_url:
                      type: string
                    state:
                      type: string
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 获取OAuth认证URL
      tags:
      - OAuth
  /auth/profile:
    get:
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/dto.GetUserProfileResponse'
        "401":
          description: 未认证
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 获取当前用户资料
      tags:
      - 认证
  /auth/recharge:
    post:
      consumes:
      - application/json
      description: 用户为自己的账户充值
      parameters:
      - description: 充值请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UserRechargeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 充值成功
          schema:
            $ref: '#/definitions/dto.GetUserProfileResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 未认证
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 用户充值
      tags:
      - 认证
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用刷新令牌获取新的访问令牌
      parameters:
      - description: 刷新令牌请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功
          schema:
            $ref: '#/definitions/dto.RefreshTokenResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 刷新令牌无效
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 刷新访问令牌
      tags:
      - 认证
  /auth/register:
    post:
      consumes:
      - application/json
      description: 注册新用户账户
      parameters:
      - description: 注册请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 注册成功
          schema:
            $ref: '#/definitions/dto.RegisterResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "409":
          description: 用户名或邮箱已存在
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 用户注册
      tags:
      - 认证
  /auth/register-with-code:
    post:
      consumes:
      - application/json
      description: 使用邮箱验证码进行用户注册
      parameters:
      - description: 注册请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RegisterWithCodeRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 注册成功
          schema:
            $ref: '#/definitions/dto.RegisterResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "409":
          description: 用户已存在
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 带验证码的用户注册
      tags:
      - 认证
  /auth/reset-password:
    post:
      consumes:
      - application/json
      description: 使用邮箱验证码重置密码
      parameters:
      - description: 重置密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功
          schema:
            $ref: '#/definitions/dto.ResetPasswordResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 重置密码
      tags:
      - 认证
  /auth/send-verification-code:
    post:
      consumes:
      - application/json
      description: 发送邮箱验证码（注册或密码重置）
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.SendVerificationCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证码发送成功
          schema:
            $ref: '#/definitions/dto.SendVerificationCodeResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "429":
          description: 发送过于频繁
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 发送验证码
      tags:
      - 认证
  /auth/verify-code:
    post:
      consumes:
      - application/json
      description: 验证邮箱验证码
      parameters:
      - description: 验证验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.VerifyCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证结果
          schema:
            $ref: '#/definitions/dto.VerifyCodeResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 验证验证码
      tags:
      - 认证
  /health:
    get:
      description: 检查服务整体健康状态，包括数据库和AI提供商连接状态
      produces:
      - application/json
      responses:
        "200":
          description: 健康检查通过
          schema:
            $ref: '#/definitions/dto.Response'
        "503":
          description: 健康检查失败
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 健康检查
      tags:
      - 健康检查
  /health/live:
    get:
      description: 检查服务是否正在运行
      produces:
      - application/json
      responses:
        "200":
          description: 服务存活
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 存活检查
      tags:
      - 健康检查
  /health/ready:
    get:
      description: 检查服务是否已准备好接收请求
      produces:
      - application/json
      responses:
        "200":
          description: 服务就绪
          schema:
            $ref: '#/definitions/dto.Response'
        "503":
          description: 服务未就绪
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 就绪检查
      tags:
      - 健康检查
  /health/stats:
    get:
      description: 获取系统运行统计信息
      produces:
      - application/json
      responses:
        "200":
          description: 统计信息
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 获取统计失败
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 系统统计
      tags:
      - 健康检查
  /health/version:
    get:
      description: 获取服务版本信息
      produces:
      - application/json
      responses:
        "200":
          description: 版本信息
          schema:
            $ref: '#/definitions/dto.Response'
      summary: 版本信息
      tags:
      - 健康检查
  /metrics:
    get:
      description: 获取Prometheus格式的监控指标
      produces:
      - text/plain
      responses:
        "200":
          description: Prometheus指标
          schema:
            type: string
      summary: 监控指标
      tags:
      - 监控
  /mj/submit/action:
    post:
      consumes:
      - application/json
      description: 执行 U1-U4、V1-V4 等操作
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 操作请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 执行操作
      tags:
      - Midjourney
  /mj/submit/blend:
    post:
      consumes:
      - application/json
      description: 上传2-5张图像并混合成新图像
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 混合请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 混合图像
      tags:
      - Midjourney
  /mj/submit/cancel:
    post:
      consumes:
      - application/json
      description: 取消正在进行的任务
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 取消请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 取消任务
      tags:
      - Midjourney
  /mj/submit/describe:
    post:
      consumes:
      - application/json
      description: 上传图像并生成四个提示词
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 描述请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 描述图像
      tags:
      - Midjourney
  /mj/submit/imagine:
    post:
      consumes:
      - application/json
      description: 根据提示词生成图像，类似 /imagine 命令
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 图像生成请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 生成图像
      tags:
      - Midjourney
  /mj/submit/modal:
    post:
      consumes:
      - application/json
      description: 对图像进行局部重绘
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 局部重绘请求
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 局部重绘
      tags:
      - Midjourney
  /mj/task/{id}/fetch:
    get:
      consumes:
      - application/json
      description: 获取任务的当前状态和结果
      parameters:
      - description: API密钥
        in: header
        name: mj-api-secret
        required: true
        type: string
      - description: 任务ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: 获取任务结果
      tags:
      - Midjourney
  /v1/chat/completions:
    post:
      consumes:
      - application/json
      description: 创建聊天补全请求，兼容OpenAI API格式。支持流式和非流式响应。
      parameters:
      - description: 聊天补全请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/clients.ChatCompletionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 聊天补全响应
          schema:
            $ref: '#/definitions/clients.AIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "429":
          description: 请求过于频繁
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 聊天补全
      tags:
      - AI接口
  /v1/completions:
    post:
      consumes:
      - application/json
      description: 创建文本补全请求，兼容OpenAI API格式
      parameters:
      - description: 文本补全请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/clients.CompletionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 文本补全响应
          schema:
            $ref: '#/definitions/clients.AIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/dto.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "429":
          description: 请求过于频繁
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 文本补全
      tags:
      - AI接口
  /v1/messages:
    post:
      consumes:
      - application/json
      description: |-
        完全兼容 Anthropic Messages API 的消息创建接口。支持文本对话、工具调用、流式响应等功能。

        **支持的功能：**
        - 文本对话（单轮和多轮）
        - 系统提示（system prompt）
        - 流式响应（Server-Sent Events）
        - 工具调用（Function Calling）
        - 温度控制、Top-K、Top-P 采样
        - 停止序列、最大token限制

        **认证方式：**
        - Bearer Token: `Authorization: Bearer YOUR_API_KEY`
        - API Key Header: `x-api-key: YOUR_API_KEY`

        **版本控制：**
        - 推荐添加版本头: `anthropic-version: 2023-06-01`

        **流式响应：**
        - 设置 `stream: true` 启用流式响应
        - 响应格式为 Server-Sent Events (text/event-stream)
        - 每个数据块以 `data: ` 开头，结束时发送 `data: [DONE]`
      parameters:
      - default: "2023-06-01"
        description: Anthropic API版本
        in: header
        name: anthropic-version
        type: string
      - description: API密钥（可替代Authorization头）
        in: header
        name: x-api-key
        type: string
      - description: Anthropic消息请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/clients.AnthropicMessageRequest'
      produces:
      - application/json
      - text/event-stream
      responses:
        "200":
          description: 流式响应 (当stream=true时)" format(text/event-stream)
          schema:
            type: string
        "400":
          description: 请求参数错误" example({"type":"error","error":{"type":"invalid_request_error","message":"model
            is required"}})
          schema:
            type: object
        "401":
          description: 认证失败" example({"type":"error","error":{"type":"authentication_error","message":"Authentication
            required"}})
          schema:
            type: object
        "429":
          description: 请求过于频繁" example({"type":"error","error":{"type":"rate_limit_error","message":"Rate
            limit exceeded"}})
          schema:
            type: object
        "500":
          description: 服务器内部错误" example({"type":"error","error":{"type":"api_error","message":"Internal
            server error"}})
          schema:
            type: object
      security:
      - BearerAuth: []
      summary: Anthropic Messages API - 创建消息
      tags:
      - AI接口
  /v1/models:
    get:
      description: 获取可用的AI模型列表
      produces:
      - application/json
      responses:
        "200":
          description: 模型列表
          schema:
            $ref: '#/definitions/clients.ModelsResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 列出模型
      tags:
      - AI接口
  /v1/usage:
    get:
      description: 获取当前用户的API使用统计
      produces:
      - application/json
      responses:
        "200":
          description: 使用统计信息
          schema:
            $ref: '#/definitions/dto.UsageResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/dto.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/dto.Response'
      security:
      - BearerAuth: []
      summary: 使用统计
      tags:
      - AI接口
securityDefinitions:
  BearerAuth:
    description: 'Bearer token for API authentication. Format: ''Bearer {token}'''
    in: header
    name: Authorization
    type: apiKey
  MJApiSecret:
    description: API secret for Midjourney endpoints
    in: header
    name: mj-api-secret
    type: apiKey
swagger: "2.0"
