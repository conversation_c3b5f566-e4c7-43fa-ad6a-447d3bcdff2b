// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "https://example.com/terms",
        "contact": {
            "name": "API Support",
            "url": "https://example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/auth/change-password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "修改当前用户的密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "修改用户密码",
                "parameters": [
                    {
                        "description": "修改密码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ChangePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "未认证或旧密码错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "description": "使用用户名和密码进行登录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "$ref": "#/definitions/dto.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/auth/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "获取当前用户资料",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "$ref": "#/definitions/dto.GetUserProfileResponse"
                        }
                    },
                    "401": {
                        "description": "未认证",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/auth/recharge": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户为自己的账户充值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户充值",
                "parameters": [
                    {
                        "description": "充值请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UserRechargeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "充值成功",
                        "schema": {
                            "$ref": "#/definitions/dto.GetUserProfileResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "未认证",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/auth/refresh": {
            "post": {
                "description": "使用刷新令牌获取新的访问令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "刷新访问令牌",
                "parameters": [
                    {
                        "description": "刷新令牌请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RefreshTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "刷新成功",
                        "schema": {
                            "$ref": "#/definitions/dto.RefreshTokenResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "刷新令牌无效",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "description": "注册新用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "注册成功",
                        "schema": {
                            "$ref": "#/definitions/dto.RegisterResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "409": {
                        "description": "用户名或邮箱已存在",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "检查服务整体健康状态，包括数据库和AI提供商连接状态",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康检查"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "健康检查通过",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "503": {
                        "description": "健康检查失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/health/live": {
            "get": {
                "description": "检查服务是否正在运行",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康检查"
                ],
                "summary": "存活检查",
                "responses": {
                    "200": {
                        "description": "服务存活",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/health/ready": {
            "get": {
                "description": "检查服务是否已准备好接收请求",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康检查"
                ],
                "summary": "就绪检查",
                "responses": {
                    "200": {
                        "description": "服务就绪",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "503": {
                        "description": "服务未就绪",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/health/stats": {
            "get": {
                "description": "获取系统运行统计信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康检查"
                ],
                "summary": "系统统计",
                "responses": {
                    "200": {
                        "description": "统计信息",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "获取统计失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/health/version": {
            "get": {
                "description": "获取服务版本信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康检查"
                ],
                "summary": "版本信息",
                "responses": {
                    "200": {
                        "description": "版本信息",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/metrics": {
            "get": {
                "description": "获取Prometheus格式的监控指标",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "监控"
                ],
                "summary": "监控指标",
                "responses": {
                    "200": {
                        "description": "Prometheus指标",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mj/submit/action": {
            "post": {
                "description": "执行 U1-U4、V1-V4 等操作",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "执行操作",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "操作请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/submit/blend": {
            "post": {
                "description": "上传2-5张图像并混合成新图像",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "混合图像",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "混合请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/submit/cancel": {
            "post": {
                "description": "取消正在进行的任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "取消任务",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "取消请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/submit/describe": {
            "post": {
                "description": "上传图像并生成四个提示词",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "描述图像",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "描述请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/submit/imagine": {
            "post": {
                "description": "根据提示词生成图像，类似 /imagine 命令",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "生成图像",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "图像生成请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/submit/modal": {
            "post": {
                "description": "对图像进行局部重绘",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "局部重绘",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "局部重绘请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/mj/task/{id}/fetch": {
            "get": {
                "description": "获取任务的当前状态和结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Midjourney"
                ],
                "summary": "获取任务结果",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥",
                        "name": "mj-api-secret",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/v1/chat/completions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建聊天补全请求，兼容OpenAI API格式。支持流式和非流式响应。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AI接口"
                ],
                "summary": "聊天补全",
                "parameters": [
                    {
                        "description": "聊天补全请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/clients.ChatCompletionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "聊天补全响应",
                        "schema": {
                            "$ref": "#/definitions/clients.AIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "429": {
                        "description": "请求过于频繁",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/v1/completions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建文本补全请求，兼容OpenAI API格式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AI接口"
                ],
                "summary": "文本补全",
                "parameters": [
                    {
                        "description": "文本补全请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/clients.CompletionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "文本补全响应",
                        "schema": {
                            "$ref": "#/definitions/clients.AIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "429": {
                        "description": "请求过于频繁",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/v1/messages": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "完全兼容 Anthropic Messages API 的消息创建接口。支持文本对话、工具调用、流式响应等功能。\n\n**支持的功能：**\n- 文本对话（单轮和多轮）\n- 系统提示（system prompt）\n- 流式响应（Server-Sent Events）\n- 工具调用（Function Calling）\n- 温度控制、Top-K、Top-P 采样\n- 停止序列、最大token限制\n\n**认证方式：**\n- Bearer Token: ` + "`" + `Authorization: Bearer YOUR_API_KEY` + "`" + `\n- API Key Header: ` + "`" + `x-api-key: YOUR_API_KEY` + "`" + `\n\n**版本控制：**\n- 推荐添加版本头: ` + "`" + `anthropic-version: 2023-06-01` + "`" + `\n\n**流式响应：**\n- 设置 ` + "`" + `stream: true` + "`" + ` 启用流式响应\n- 响应格式为 Server-Sent Events (text/event-stream)\n- 每个数据块以 ` + "`" + `data: ` + "`" + ` 开头，结束时发送 ` + "`" + `data: [DONE]` + "`" + `",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json",
                    "text/event-stream"
                ],
                "tags": [
                    "AI接口"
                ],
                "summary": "Anthropic Messages API - 创建消息",
                "parameters": [
                    {
                        "type": "string",
                        "default": "2023-06-01",
                        "description": "Anthropic API版本",
                        "name": "anthropic-version",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "API密钥（可替代Authorization头）",
                        "name": "x-api-key",
                        "in": "header"
                    },
                    {
                        "description": "Anthropic消息请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/clients.AnthropicMessageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "流式响应 (当stream=true时)\" format(text/event-stream)",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "请求参数错误\" example({\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"model is required\"}})",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "401": {
                        "description": "认证失败\" example({\"type\":\"error\",\"error\":{\"type\":\"authentication_error\",\"message\":\"Authentication required\"}})",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "429": {
                        "description": "请求过于频繁\" example({\"type\":\"error\",\"error\":{\"type\":\"rate_limit_error\",\"message\":\"Rate limit exceeded\"}})",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误\" example({\"type\":\"error\",\"error\":{\"type\":\"api_error\",\"message\":\"Internal server error\"}})",
                        "schema": {
                            "type": "object"
                        }
                    }
                }
            }
        },
        "/v1/models": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取可用的AI模型列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AI接口"
                ],
                "summary": "列出模型",
                "responses": {
                    "200": {
                        "description": "模型列表",
                        "schema": {
                            "$ref": "#/definitions/clients.ModelsResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        },
        "/v1/usage": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的API使用统计",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AI接口"
                ],
                "summary": "使用统计",
                "responses": {
                    "200": {
                        "description": "使用统计信息",
                        "schema": {
                            "$ref": "#/definitions/dto.UsageResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.Response"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "clients.AIChoice": {
            "type": "object",
            "properties": {
                "finish_reason": {
                    "type": "string"
                },
                "index": {
                    "type": "integer"
                },
                "message": {
                    "$ref": "#/definitions/clients.AIMessage"
                },
                "text": {
                    "type": "string"
                },
                "tool_calls": {
                    "description": "Function calls in streaming mode",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.ToolCall"
                    }
                }
            }
        },
        "clients.AIError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "clients.AIMessage": {
            "type": "object",
            "required": [
                "role"
            ],
            "properties": {
                "content": {
                    "type": "string",
                    "example": "Hello, how are you?"
                },
                "name": {
                    "description": "Name of the function for tool messages",
                    "type": "string"
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "system",
                        "user",
                        "assistant",
                        "tool"
                    ],
                    "example": "user"
                },
                "tool_call_id": {
                    "description": "ID of the tool call this message is responding to",
                    "type": "string"
                },
                "tool_calls": {
                    "description": "Function calls made by assistant",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.ToolCall"
                    }
                }
            }
        },
        "clients.AIModel": {
            "type": "object",
            "properties": {
                "created": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "object": {
                    "type": "string"
                },
                "owned_by": {
                    "type": "string"
                },
                "permission": {
                    "type": "array",
                    "items": {}
                }
            }
        },
        "clients.AIResponse": {
            "type": "object",
            "properties": {
                "choices": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.AIChoice"
                    }
                },
                "created": {
                    "type": "integer"
                },
                "error": {
                    "$ref": "#/definitions/clients.AIError"
                },
                "id": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "object": {
                    "type": "string"
                },
                "usage": {
                    "$ref": "#/definitions/clients.AIUsage"
                }
            }
        },
        "clients.AIUsage": {
            "type": "object",
            "properties": {
                "completion_tokens": {
                    "type": "integer"
                },
                "prompt_tokens": {
                    "type": "integer"
                },
                "total_tokens": {
                    "type": "integer"
                }
            }
        },
        "clients.AnthropicCacheControl": {
            "type": "object",
            "properties": {
                "ttl": {
                    "description": "\"5m\", \"1h\"",
                    "type": "string"
                },
                "type": {
                    "description": "\"ephemeral\"",
                    "type": "string"
                }
            }
        },
        "clients.AnthropicCacheCreation": {
            "type": "object",
            "properties": {
                "ephemeral_1h_input_tokens": {
                    "type": "integer"
                },
                "ephemeral_5m_input_tokens": {
                    "type": "integer"
                }
            }
        },
        "clients.AnthropicContainer": {
            "type": "object",
            "properties": {
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                }
            }
        },
        "clients.AnthropicContentBlock": {
            "type": "object",
            "properties": {
                "content": {},
                "id": {
                    "type": "string",
                    "example": "toolu_01A09q90qw90lq917835lq9"
                },
                "input": {},
                "is_error": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string",
                    "example": "get_weather"
                },
                "source": {
                    "type": "object",
                    "properties": {
                        "data": {
                            "type": "string"
                        },
                        "media_type": {
                            "type": "string",
                            "example": "image/jpeg"
                        },
                        "type": {
                            "type": "string",
                            "example": "base64"
                        }
                    }
                },
                "text": {
                    "type": "string",
                    "example": "Hello, how can I help you?"
                },
                "tool_use_id": {
                    "type": "string"
                },
                "type": {
                    "type": "string",
                    "example": "text"
                }
            }
        },
        "clients.AnthropicMCPServer": {
            "type": "object",
            "required": [
                "name",
                "type",
                "url"
            ],
            "properties": {
                "authorization_token": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "tool_configuration": {
                    "$ref": "#/definitions/clients.AnthropicToolConfiguration"
                },
                "type": {
                    "description": "\"url\"",
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "clients.AnthropicMessage": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "example": "Hello, how are you?"
                },
                "role": {
                    "type": "string",
                    "example": "user"
                }
            }
        },
        "clients.AnthropicMessageRequest": {
            "type": "object",
            "required": [
                "max_tokens",
                "messages",
                "model"
            ],
            "properties": {
                "container": {
                    "type": "string"
                },
                "max_tokens": {
                    "type": "integer",
                    "maximum": 4096,
                    "minimum": 1,
                    "example": 1024
                },
                "mcp_servers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.AnthropicMCPServer"
                    }
                },
                "messages": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/clients.AnthropicMessage"
                    }
                },
                "metadata": {
                    "$ref": "#/definitions/clients.AnthropicMetadata"
                },
                "model": {
                    "type": "string",
                    "example": "claude-3-sonnet-20240229"
                },
                "service_tier": {
                    "type": "string",
                    "example": "auto"
                },
                "stop_sequences": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"\\n\\n\"]"
                    ]
                },
                "stream": {
                    "type": "boolean",
                    "example": false
                },
                "system": {
                    "type": "string",
                    "example": "You are a helpful assistant."
                },
                "temperature": {
                    "type": "number",
                    "maximum": 1,
                    "minimum": 0,
                    "example": 0.7
                },
                "thinking": {
                    "$ref": "#/definitions/clients.AnthropicThinkingConfig"
                },
                "tool_choice": {
                    "type": "string",
                    "example": "auto"
                },
                "tools": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.AnthropicTool"
                    }
                },
                "top_k": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 5
                },
                "top_p": {
                    "type": "number",
                    "maximum": 1,
                    "minimum": 0,
                    "example": 0.7
                }
            }
        },
        "clients.AnthropicMessageResponse": {
            "type": "object",
            "properties": {
                "container": {
                    "$ref": "#/definitions/clients.AnthropicContainer"
                },
                "content": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.AnthropicContentBlock"
                    }
                },
                "id": {
                    "type": "string",
                    "example": "msg_013Zva2CMHLNnXjNJJKqJ2EF"
                },
                "model": {
                    "type": "string",
                    "example": "claude-3-sonnet-20240229"
                },
                "role": {
                    "type": "string",
                    "example": "assistant"
                },
                "stop_reason": {
                    "type": "string",
                    "example": "end_turn"
                },
                "stop_sequence": {
                    "type": "string"
                },
                "type": {
                    "type": "string",
                    "example": "message"
                },
                "usage": {
                    "$ref": "#/definitions/clients.AnthropicUsage"
                }
            }
        },
        "clients.AnthropicMetadata": {
            "type": "object",
            "properties": {
                "user_id": {
                    "description": "外部用户标识符",
                    "type": "string"
                }
            }
        },
        "clients.AnthropicServerToolUse": {
            "type": "object",
            "properties": {
                "web_search_requests": {
                    "type": "integer"
                }
            }
        },
        "clients.AnthropicThinkingConfig": {
            "type": "object",
            "required": [
                "budget_tokens",
                "type"
            ],
            "properties": {
                "budget_tokens": {
                    "type": "integer",
                    "minimum": 1024
                },
                "type": {
                    "description": "\"enabled\"",
                    "type": "string"
                }
            }
        },
        "clients.AnthropicTool": {
            "type": "object",
            "required": [
                "input_schema",
                "name"
            ],
            "properties": {
                "cache_control": {
                    "$ref": "#/definitions/clients.AnthropicCacheControl"
                },
                "description": {
                    "type": "string"
                },
                "input_schema": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "description": "\"custom\" 等",
                    "type": "string"
                }
            }
        },
        "clients.AnthropicToolConfiguration": {
            "type": "object",
            "properties": {
                "allowed_tools": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "enabled": {
                    "type": "boolean"
                }
            }
        },
        "clients.AnthropicUsage": {
            "type": "object",
            "properties": {
                "cache_creation": {
                    "$ref": "#/definitions/clients.AnthropicCacheCreation"
                },
                "cache_creation_input_tokens": {
                    "type": "integer"
                },
                "cache_read_input_tokens": {
                    "type": "integer"
                },
                "input_tokens": {
                    "type": "integer",
                    "example": 10
                },
                "output_tokens": {
                    "type": "integer",
                    "example": 25
                },
                "server_tool_use": {
                    "$ref": "#/definitions/clients.AnthropicServerToolUse"
                },
                "service_tier": {
                    "type": "string",
                    "example": "standard"
                }
            }
        },
        "clients.ChatCompletionRequest": {
            "type": "object",
            "required": [
                "messages",
                "model"
            ],
            "properties": {
                "max_tokens": {
                    "type": "integer",
                    "example": 150
                },
                "messages": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/clients.AIMessage"
                    }
                },
                "model": {
                    "type": "string",
                    "example": "gpt-3.5-turbo"
                },
                "stream": {
                    "type": "boolean",
                    "example": false
                },
                "temperature": {
                    "type": "number",
                    "example": 0.7
                },
                "tool_choice": {
                    "description": "Tool choice strategy"
                },
                "tools": {
                    "description": "Function call tools",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.Tool"
                    }
                },
                "web_search": {
                    "description": "是否启用联网搜索",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "clients.CompletionRequest": {
            "type": "object",
            "required": [
                "model",
                "prompt"
            ],
            "properties": {
                "max_tokens": {
                    "type": "integer",
                    "example": 150
                },
                "model": {
                    "type": "string",
                    "example": "gpt-3.5-turbo"
                },
                "prompt": {
                    "type": "string",
                    "example": "Once upon a time"
                },
                "stream": {
                    "type": "boolean",
                    "example": false
                },
                "temperature": {
                    "type": "number",
                    "example": 0.7
                },
                "web_search": {
                    "description": "是否启用联网搜索",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "clients.Function": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Search for information"
                },
                "name": {
                    "type": "string",
                    "example": "search"
                },
                "parameters": {
                    "description": "JSON Schema for function parameters"
                }
            }
        },
        "clients.FunctionCall": {
            "type": "object",
            "properties": {
                "arguments": {
                    "description": "JSON string of function arguments",
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "example": "search"
                }
            }
        },
        "clients.ModelsResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/clients.AIModel"
                    }
                },
                "object": {
                    "type": "string"
                }
            }
        },
        "clients.Tool": {
            "type": "object",
            "properties": {
                "function": {
                    "$ref": "#/definitions/clients.Function"
                },
                "type": {
                    "type": "string",
                    "example": "function"
                }
            }
        },
        "clients.ToolCall": {
            "type": "object",
            "properties": {
                "function": {
                    "$ref": "#/definitions/clients.FunctionCall"
                },
                "id": {
                    "type": "string",
                    "example": "call_123"
                },
                "type": {
                    "type": "string",
                    "example": "function"
                }
            }
        },
        "dto.ChangePasswordRequest": {
            "type": "object",
            "required": [
                "new_password",
                "old_password"
            ],
            "properties": {
                "new_password": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 6
                },
                "old_password": {
                    "type": "string"
                }
            }
        },
        "dto.ErrorInfo": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "details": {
                    "type": "object",
                    "additionalProperties": true
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dto.GetUserProfileResponse": {
            "type": "object",
            "properties": {
                "balance": {
                    "type": "number"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.LoginRequest": {
            "type": "object",
            "required": [
                "password",
                "username"
            ],
            "properties": {
                "password": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 6
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "dto.LoginResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "expires_in": {
                    "type": "integer"
                },
                "refresh_token": {
                    "type": "string"
                },
                "token_type": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/dto.UserInfo"
                }
            }
        },
        "dto.RefreshTokenRequest": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "dto.RefreshTokenResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "expires_in": {
                    "type": "integer"
                },
                "refresh_token": {
                    "type": "string"
                },
                "token_type": {
                    "type": "string"
                }
            }
        },
        "dto.RegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "password": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 6
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "dto.RegisterResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.Response": {
            "type": "object",
            "properties": {
                "data": {},
                "error": {
                    "$ref": "#/definitions/dto.ErrorInfo"
                },
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "dto.UsageResponse": {
            "type": "object",
            "properties": {
                "total_cost": {
                    "type": "number",
                    "example": 1.25
                },
                "total_requests": {
                    "type": "integer",
                    "example": 100
                },
                "total_tokens": {
                    "type": "integer",
                    "example": 5000
                }
            }
        },
        "dto.UserInfo": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.UserRechargeRequest": {
            "type": "object",
            "required": [
                "amount"
            ],
            "properties": {
                "amount": {
                    "type": "number"
                },
                "description": {
                    "type": "string"
                }
            }
        },
        "handlers.ActionRequest": {
            "type": "object",
            "required": [
                "customId",
                "taskId"
            ],
            "properties": {
                "customId": {
                    "type": "string"
                },
                "notifyHook": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "taskId": {
                    "type": "string"
                }
            }
        },
        "handlers.BlendRequest": {
            "type": "object",
            "required": [
                "base64Array"
            ],
            "properties": {
                "base64Array": {
                    "type": "array",
                    "maxItems": 5,
                    "minItems": 2,
                    "items": {
                        "type": "string"
                    }
                },
                "botType": {
                    "type": "string"
                },
                "dimensions": {
                    "type": "string"
                },
                "notifyHook": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "handlers.CancelRequest": {
            "type": "object",
            "required": [
                "taskId"
            ],
            "properties": {
                "taskId": {
                    "type": "string"
                }
            }
        },
        "handlers.DescribeRequest": {
            "type": "object",
            "required": [
                "base64"
            ],
            "properties": {
                "base64": {
                    "type": "string"
                },
                "botType": {
                    "type": "string"
                },
                "notifyHook": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "handlers.ImagineRequest": {
            "type": "object",
            "required": [
                "prompt"
            ],
            "properties": {
                "base64Array": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "botType": {
                    "type": "string"
                },
                "notifyHook": {
                    "type": "string"
                },
                "prompt": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "handlers.MJButton": {
            "type": "object",
            "properties": {
                "customId": {
                    "type": "string"
                },
                "emoji": {
                    "type": "string"
                },
                "label": {
                    "type": "string"
                },
                "style": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                }
            }
        },
        "handlers.MJImageURL": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                }
            }
        },
        "handlers.MJResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "properties": {
                    "type": "object",
                    "additionalProperties": true
                },
                "result": {}
            }
        },
        "handlers.MJTaskResponse": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "botType": {
                    "type": "string"
                },
                "buttons": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.MJButton"
                    }
                },
                "customId": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "failReason": {
                    "type": "string"
                },
                "finishTime": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "imageHeight": {
                    "type": "integer"
                },
                "imageUrl": {
                    "type": "string"
                },
                "imageUrls": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.MJImageURL"
                    }
                },
                "imageWidth": {
                    "type": "integer"
                },
                "maskBase64": {
                    "type": "string"
                },
                "mode": {
                    "type": "string"
                },
                "progress": {
                    "type": "string"
                },
                "prompt": {
                    "type": "string"
                },
                "promptEn": {
                    "type": "string"
                },
                "proxy": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                },
                "state": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "submitTime": {
                    "type": "integer"
                },
                "videoUrl": {
                    "type": "string"
                },
                "videoUrls": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "handlers.ModalRequest": {
            "type": "object",
            "required": [
                "base64",
                "maskBase64",
                "prompt"
            ],
            "properties": {
                "base64": {
                    "type": "string"
                },
                "botType": {
                    "type": "string"
                },
                "maskBase64": {
                    "type": "string"
                },
                "notifyHook": {
                    "type": "string"
                },
                "prompt": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Bearer token for API authentication. Format: 'Bearer {token}'",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "MJApiSecret": {
            "description": "API secret for Midjourney endpoints",
            "type": "apiKey",
            "name": "mj-api-secret",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "api-dev.718ai.cn",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "AI API Gateway",
	Description:      "AI API Gateway provides unified access to multiple AI services including OpenAI-compatible chat completions, Midjourney image generation, and more.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
